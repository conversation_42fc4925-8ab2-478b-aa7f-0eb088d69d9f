#!/bin/bash


BACKUP_DIR="/opt/mysql_backup/backups"
DATE=$(date +"%Y-%m-%d")
MYSQL_USER="root"
MYSQL_PASSWORD="Admin@12345"
MYSQL_HOST="localhost"
MYSQL_PORT=3306
DATABASE="mysql"
FILENAME="$BACKUP_DIR/${DATABASE}_$DATE.sql"

# 创建备份目录
mkdir -p "$BACKUP_DIR"

# 执行备份
mysqldump -h "$MYSQL_HOST" -P "$MYSQL_PORT" -u "$MYSQL_USER" -p"$MYSQL_PASSWORD" --single-transaction "$DATABASE" > "$FILENAME"

# 压缩
gzip "$FILENAME"

# 删除30天前的备份
find "$BACKUP_DIR" -type f -name "${DATABASE}_*.sql.gz" -mtime +30 -delete

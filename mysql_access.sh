#!/bin/bash

# 配置路径
LOG_DIR="/opt/mysql/logs"
STATE_DIR="/opt/mysql/monitor_state"
EVENT_LOG="$LOG_DIR/mysql_check.log"
DEBUG_LOG="/tmp/mysql_monitor_debug.log"

ERROR_LOG="$LOG_DIR/error.log"
GENERAL_LOG="$LOG_DIR/general.log"

# 阈值与时间窗口
FAIL_THRESHOLD=3
SUCCESS_THRESHOLD=15
TIME_WINDOW=300  # 秒

# 白名单（可填网段/IP）
IP_WHITELIST=( "" "" )

# 创建所需目录和日志文件
mkdir -p "$STATE_DIR"
touch "$ERROR_LOG" "$GENERAL_LOG" "$EVENT_LOG" "$DEBUG_LOG"

# 获取文件大小
get_current_size() {
    [ -f "$1" ] && stat -c %s "$1" || echo 0
}

# 记录已读位置
record_position() {
    echo $2 > "$STATE_DIR/$(basename $1).pos"
}

# 获取上次已读位置
get_last_position() {
    local pos_file="$STATE_DIR/$(basename $1).pos"
    [ -f "$pos_file" ] && cat "$pos_file" || echo 0
}

# 判断 IP 是否在白名单
is_whitelisted() {
    local ip=$1
    for subnet in "${IP_WHITELIST[@]}"; do
        [[ -z "$subnet" ]] && continue
        if [[ "$subnet" == *"/"* ]]; then
            IFS='/' read -r net mask <<< "$subnet"
            [[ $(ipcalc -n "$ip/$mask" | awk -F= '/NETWORK/ {print $2}') == "$net" ]] && return 0
        else
            [[ "$ip" == "$subnet" ]] && return 0
        fi
    done
    return 1
}

# 分析失败日志
analyze_error_log() {
    local last_pos=$(get_last_position "$ERROR_LOG")
    local current_pos=$(get_current_size "$ERROR_LOG")
    [ "$last_pos" -gt "$current_pos" ] && last_pos=0

    if [ "$current_pos" -gt "$last_pos" ]; then
        tail -c +$((last_pos + 1)) "$ERROR_LOG" | grep -E "Access denied|ERROR 1045" | while read -r line; do
            local ip=""
            if [[ "$line" =~ Access\ denied\ for\ user\ .*\@\'([^\']+)\' ]]; then
                ip_or_host=${BASH_REMATCH[1]}
                if [[ "$ip_or_host" == "localhost" ]]; then
                    ip="127.0.0.1"
                elif [[ "$ip_or_host" =~ ^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
                    ip="$ip_or_host"
                else
                    ip=$(getent hosts "$ip_or_host" | awk '{print $1}' | head -1)
                    [ -z "$ip" ] && ip="$ip_or_host"
                fi
            fi
            echo "DEBUG [FAIL]: $line -> IP: $ip" >> "$DEBUG_LOG"
            [ -z "$ip" ] && continue
            is_whitelisted "$ip" && continue
            echo "$(date +%s) $ip failed" >> "$STATE_DIR/login_attempts.log"
        done
        record_position "$ERROR_LOG" "$current_pos"
    fi
}

# 分析成功日志
analyze_general_log() {
    local last_pos=$(get_last_position "$GENERAL_LOG")
    local current_pos=$(get_current_size "$GENERAL_LOG")
    [ "$last_pos" -gt "$current_pos" ] && last_pos=0

    if [ "$current_pos" -gt "$last_pos" ]; then
        tail -c +$((last_pos + 1)) "$GENERAL_LOG" | grep "Connect" | while read -r line; do
            local ip=""
            if [[ "$line" =~ @([0-9]+\.[0-9]+\.[0-9]+\.[0-9]+) ]]; then
                ip="${BASH_REMATCH[1]}"
            elif [[ "$line" =~ @([a-zA-Z0-9\.\-]+) ]]; then
                ip_or_host="${BASH_REMATCH[1]}"
                ip=$(getent hosts "$ip_or_host" | awk '{print $1}' | head -1)
            fi
            echo "DEBUG [SUCCESS]: $line -> IP: $ip" >> "$DEBUG_LOG"
            [ -z "$ip" ] && continue
            is_whitelisted "$ip" && continue
            echo "$(date +%s) $ip success" >> "$STATE_DIR/login_attempts.log"
        done
        record_position "$GENERAL_LOG" "$current_pos"
    fi
}

# 检测异常登录行为
detect_abnormal_logins() {
    local now=$(date +%s)
    local threshold=$((now - TIME_WINDOW))
    declare -A failed_counts success_counts

    [ ! -f "$STATE_DIR/login_attempts.log" ] && return

    while read -r line; do
        local timestamp=${line%% *}
        [ "$timestamp" -lt "$threshold" ] && continue
        local ip=${line#* }; ip=${ip% *}
        local status=${line##* }
        [[ "$status" == "failed" ]] && ((failed_counts[$ip]++))
        [[ "$status" == "success" ]] && ((success_counts[$ip]++))
    done < "$STATE_DIR/login_attempts.log"

    for ip in "${!failed_counts[@]}"; do
        echo "DEBUG [COUNT] $ip failed: ${failed_counts[$ip]}" >> "$DEBUG_LOG"
        if [ "${failed_counts[$ip]}" -ge "$FAIL_THRESHOLD" ]; then
            echo "$(date '+%F %T') [FAILED LOGIN] IP: $ip, Count: ${failed_counts[$ip]}" >> "$EVENT_LOG"
        fi
    done

    for ip in "${!success_counts[@]}"; do
        echo "DEBUG [COUNT] $ip success: ${success_counts[$ip]}" >> "$DEBUG_LOG"
        if [ "${success_counts[$ip]}" -ge "$SUCCESS_THRESHOLD" ]; then
            echo "$(date '+%F %T') [SUCCESS LOGIN] IP: $ip, Count: ${success_counts[$ip]}" >> "$EVENT_LOG"
        fi
    done
}

# 清理过期记录
cleanup_old_records() {
    local now=$(date +%s)
    local cutoff=$((now - TIME_WINDOW * 2))
    [ -f "$STATE_DIR/login_attempts.log" ] || return
    awk -v cutoff="$cutoff" '$1 >= cutoff' "$STATE_DIR/login_attempts.log" > "$STATE_DIR/tmp_attempts.log"
    mv "$STATE_DIR/tmp_attempts.log" "$STATE_DIR/login_attempts.log"
}

main() {
    echo "===== $(date '+%F %T') MySQL Monitor Start =====" >> "$DEBUG_LOG"
    analyze_error_log
    analyze_general_log
    detect_abnormal_logins
    cleanup_old_records
    echo "===== $(date '+%F %T') MySQL Monitor End =====" >> "$DEBUG_LOG"
}

main

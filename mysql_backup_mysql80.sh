#!/bin/bash

# 设置变量
BACKUP_DIR="/opt/mysql/backup"
DB_NAME="MR-TADF_DATA"
MYSQL_CONTAINER="mysql80"
MYSQL_USER="root"
MYSQL_PASSWORD="Admin@12345"
DATE=$(date +"%Y-%m-%d")
BACKUP_FILE="$BACKUP_DIR/${DB_NAME}_$DATE.sql"

# 创建备份目录（如不存在）
mkdir -p "$BACKUP_DIR"

# 执行容器内备份命令
docker exec "$MYSQL_CONTAINER" mysqldump -u"$MYSQL_USER" -p"$MYSQL_PASSWORD" "$DB_NAME" > "$BACKUP_FILE"

# 压缩
gzip "$BACKUP_FILE"

# 可选：删除7天前的备份文件
find "$BACKUP_DIR" -type f -name "${DB_NAME}_*.sql" -mtime +7 -exec rm {} \;

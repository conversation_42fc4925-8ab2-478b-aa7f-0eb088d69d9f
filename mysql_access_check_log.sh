#!/bin/bash

# 配置路径
LOG_DIR="/var/log/mysql"
ERROR_LOG="$LOG_DIR/error.log"
GENERAL_LOG="$LOG_DIR/general.log"
STATE_DIR="/var/lib/mysql_monitor"
EVENT_LOG="$LOG_DIR/mysql_check.log"
DEBUG_LOG="/tmp/mysql_monitor_debug.log"

# 阈值与时间窗口
FAIL_THRESHOLD=3
SUCCESS_THRESHOLD=15
TIME_WINDOW=300  # 秒

# IP 白名单
IP_WHITELIST=( "" "" "" )

# 初始化目录
mkdir -p "$STATE_DIR"
touch "$ERROR_LOG" "$GENERAL_LOG" "$EVENT_LOG" "$DEBUG_LOG"

# 获取当前文件大小
get_current_size() {
    [ -f "$1" ] && stat -c %s "$1" || echo 0
}

# 保存读取位置
record_position() {
    echo $2 > "$STATE_DIR/$(basename $1).pos"
}

# 获取上次读取位置
get_last_position() {
    local pos_file="$STATE_DIR/$(basename $1).pos"
    [ -f "$pos_file" ] && cat "$pos_file" || echo 0
}

# 判断是否为白名单 IP
is_whitelisted() {
    local ip=$1
    for subnet in "${IP_WHITELIST[@]}"; do
        [[ -z "$subnet" ]] && continue
        if [[ "$subnet" == *"/"* ]]; then
            IFS='/' read -r net mask <<< "$subnet"
            [[ $(echo "$ip" | awk -F. -v m="$mask" 'BEGIN{OFS=FS} {for(i=1;i<=4;i++) if(i<=m/8)$i=$i; else $i="0"}1') == "$net" ]] && return 0
        else
            [[ "$ip" == "$subnet" ]] && return 0
        fi
    done
    return 1
}

# 提取登录失败记录
analyze_error_log() {
    local last_pos=$(get_last_position "$ERROR_LOG")
    local current_pos=$(get_current_size "$ERROR_LOG")
    [ "$last_pos" -gt "$current_pos" ] && last_pos=0

    if [ "$current_pos" -gt "$last_pos" ]; then
        tail -c +$((last_pos + 1)) "$ERROR_LOG" | grep -E "Access denied|ERROR 1045" | while read -r line; do
            local ip=""
            if [[ "$line" =~ Access\ denied\ for\ user\ .*\@\'([^\']+)\' ]]; then
                ip_or_host=${BASH_REMATCH[1]}
                if [[ "$ip_or_host" == "localhost" ]]; then
                    ip="127.0.0.1"
                elif [[ "$ip_or_host" =~ ^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
                    ip="$ip_or_host"
                else
                    ip=$(getent hosts "$ip_or_host" | awk '{print $1}' | head -1)
                    [ -z "$ip" ] && ip="$ip_or_host"
                fi
            fi
            echo "DEBUG [FAIL]: $line -> IP: $ip" >> "$DEBUG_LOG"
            [ -z "$ip" ] && continue
            is_whitelisted "$ip" && continue
            echo "$(date +%s) $ip failed" >> "$STATE_DIR/login_attempts.log"
        done
        record_position "$ERROR_LOG" "$current_pos"
    fi
}

# 提取登录成功记录
analyze_general_log() {
    local last_pos=$(get_last_position "$GENERAL_LOG")
    local current_pos=$(get_current_size "$GENERAL_LOG")
    [ "$last_pos" -gt "$current_pos" ] && last_pos=0

    if [ "$current_pos" -gt "$last_pos" ]; then
        tail -c +$((last_pos + 1)) "$GENERAL_LOG" | grep "Connect" | while read -r line; do
            local ip=""
            if [[ "$line" =~ @([^\ ]+) ]]; then
                ip_or_host=${BASH_REMATCH[1]}
                if [[ "$ip_or_host" == "localhost" ]]; then
                    ip="127.0.0.1"
                elif [[ "$ip_or_host" =~ ^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
                    ip="$ip_or_host"
                else
                    ip=$(getent hosts "$ip_or_host" | awk '{print $1}' | head -1)
                    [ -z "$ip" ] && ip="$ip_or_host"
                fi
            fi
            echo "DEBUG [SUCCESS]: $line -> IP: $ip" >> "$DEBUG_LOG"
            [ -z "$ip" ] && continue
            is_whitelisted "$ip" && continue
            echo "$(date +%s) $ip success" >> "$STATE_DIR/login_attempts.log"
        done
        record_position "$GENERAL_LOG" "$current_pos"
    fi
}

# 检测异常登录行为
detect_abnormal_logins() {
    local now=$(date +%s)
    local time_threshold=$((now - TIME_WINDOW))
    declare -A failed_counts success_counts

    if [ -f "$STATE_DIR/login_attempts.log" ]; then
        while read -r line; do
            local timestamp=${line%% *}
            [ "$timestamp" -lt "$time_threshold" ] && continue
            local ip=${line#* }; ip=${ip% *}
            local status=${line##* }
            if [ "$status" == "failed" ]; then
                failed_counts["$ip"]=$((failed_counts["$ip"] + 1))
            else
                success_counts["$ip"]=$((success_counts["$ip"] + 1))
            fi
        done < <(grep -E "^[0-9]+ .+ (failed|success)$" "$STATE_DIR/login_attempts.log")
    fi

    for ip in "${!failed_counts[@]}"; do
        echo "DEBUG [COUNT] $ip failed: ${failed_counts[$ip]}" >> "$DEBUG_LOG"
        if [ "${failed_counts[$ip]}" -ge "$FAIL_THRESHOLD" ]; then
            echo "$(date '+%F %T') [FAILED LOGIN] IP: $ip, Count: ${failed_counts[$ip]}, Threshold: $FAIL_THRESHOLD" >> "$EVENT_LOG"
        fi
    done

    for ip in "${!success_counts[@]}"; do
        echo "DEBUG [COUNT] $ip success: ${success_counts[$ip]}" >> "$DEBUG_LOG"
        if [ "${success_counts[$ip]}" -ge "$SUCCESS_THRESHOLD" ]; then
            echo "$(date '+%F %T') [SUCCESS LOGIN] IP: $ip, Count: ${success_counts[$ip]}, Threshold: $SUCCESS_THRESHOLD" >> "$EVENT_LOG"
        fi
    done
}

# 清理旧记录
cleanup_old_records() {
    local now=$(date +%s)
    local cutoff=$((now - TIME_WINDOW * 2))
    if [ -f "$STATE_DIR/login_attempts.log" ]; then
        awk -v cutoff="$cutoff" '$1 >= cutoff' "$STATE_DIR/login_attempts.log" > "$STATE_DIR/tmp_attempts.log"
        mv "$STATE_DIR/tmp_attempts.log" "$STATE_DIR/login_attempts.log"
    fi
}

# 主流程
main() {
    echo "===== $(date '+%F %T') MySQL Monitor Start =====" >> "$DEBUG_LOG"
    analyze_error_log
    analyze_general_log
    detect_abnormal_logins
    cleanup_old_records
    echo "===== $(date '+%F %T') MySQL Monitor End =====" >> "$DEBUG_LOG"
}

main

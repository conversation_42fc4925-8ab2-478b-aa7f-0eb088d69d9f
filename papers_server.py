from flask import Flask, send_from_directory
import os
import subprocess
from datetime import datetime

app = Flask(__name__)

PAPERS_DIR = "/mnt/usb2_2-0/Public/Downloads/papers"

@app.route('/')
def list_papers():
    try:
        files = os.listdir(PAPERS_DIR)
        pdf_files = [f for f in files if f.endswith('.pdf')]
        total_size = sum(os.path.getsize(os.path.join(PAPERS_DIR, f)) for f in pdf_files)
        
        html = f"""
        <html>
        <head><title>Papers文件夹</title></head>
        <body>
        <h3>Papers文件夹</h3>
        <p>📁 PDF文件数量: {len(pdf_files)} 个</p>
        <p>💾 总大小: {total_size / 1024 / 1024:.2f} MB</p>
        <p><a href="/download_all" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">📦 打包下载所有PDF</a></p>
        <hr>
        <ul>
        """
        for f in files:
            html += f'<li><a href="/{f}">{f}</a></li>'
        html += """
        </ul>
        </body>
        </html>
        """
        return html
    except Exception as e:
        return f"错误: {str(e)}"

@app.route('/download_all')
def download_all():
    """打包下载整个papers目录"""
    try:
        print("开始打包下载...")
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        archive_name = f"papers_collection_{timestamp}.tar"  # 不压缩，更快
        archive_path = f"/mnt/usb2_2-0/{archive_name}"
        
        print(f"创建压缩包: {archive_path}")
        
        # 不压缩，只打包（更快）
        result = subprocess.run([
            'tar', '-cf', archive_path,
            '-C', '/mnt/usb2_2-0/Public/Downloads',
            'papers'
        ], capture_output=True, text=True, timeout=600)
        
        print(f"tar命令返回码: {result.returncode}")
        if result.stderr:
            print(f"tar错误: {result.stderr}")
        
        if result.returncode != 0:
            return f"打包失败: {result.stderr}"
        
        # 检查文件是否生成
        if os.path.exists(archive_path):
            file_size = os.path.getsize(archive_path)
            print(f"压缩包已生成，大小: {file_size / 1024 / 1024:.2f} MB")
        else:
            print("压缩包文件不存在")
            return "错误: 压缩包生成失败"
        
        print(f"开始发送文件: {archive_name}")
        return send_from_directory('/mnt/usb2_2-0', archive_name, as_attachment=True)
        
    except subprocess.TimeoutExpired:
        print("打包超时")
        return "打包超时，请稍后重试"
    except Exception as e:
        print(f"异常: {str(e)}")
        return f"打包失败: {str(e)}"

@app.route('/<path:filename>')
def serve_paper(filename):
    return send_from_directory(PAPERS_DIR, filename, as_attachment=True)

if __name__ == "__main__":
    app.run(host="0.0.0.0", port=8080, debug=False)







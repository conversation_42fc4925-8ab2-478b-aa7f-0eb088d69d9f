#!/bin/bash

MY_ID=$1
TOTAL_NODE=$2
TORRENT_DIR="/opt/torrents/torrents"
ARIA2_RPC="http://127.0.0.1:6800/jsonrpc"
RPC_TOKEN="token:"  # 无密码格式

if [ -z "$MY_ID" ] || [ -z "$TOTAL_NODE" ]; then
  echo "用法: $0 <本机编号0开始> <总路由数>"
  exit 1
fi

ALL_TORRENTS=($(ls -1 "$TORRENT_DIR"/*.torrent | sort -r))
TOTAL=${#ALL_TORRENTS[@]}
BATCH_SIZE=100

function add_torrent() {
  local torrent_file=$1
  local json_payload=$2

  # 创建临时文件存放json
  local tmpfile=$(mktemp)
  echo "$json_payload" > "$tmpfile"

  for ((try=1; try<=3; try++)); do
    response=$(curl -s -H "Content-Type: application/json" --data-binary @"$tmpfile" "$ARIA2_RPC")
    if echo "$response" | grep -q '"result"'; then
      echo "✅ 成功添加：$(basename "$torrent_file")"
      rm -f "$tmpfile"
      return 0
    else
      echo "⚠️ 添加失败，重试第 $try 次: $(basename "$torrent_file")"
      sleep 1
    fi
  done

  echo "❌ 添加失败，跳过：$(basename "$torrent_file")"
  rm -f "$tmpfile"
  return 1
}

for ((start=0; start<TOTAL; start+=BATCH_SIZE)); do
  end=$((start + BATCH_SIZE))
  if [ $end -gt $TOTAL ]; then
    end=$TOTAL
  fi
  BATCH=("${ALL_TORRENTS[@]:start:end - start}")
  BATCH_COUNT=${#BATCH[@]}

  PER_NODE=$(( (BATCH_COUNT + TOTAL_NODE -1) / TOTAL_NODE ))
  OFFSET=$((MY_ID * PER_NODE))

  for ((i=0; i<PER_NODE; i++)); do
    INDEX=$((OFFSET + i))
    if [ $INDEX -ge $BATCH_COUNT ]; then
      break
    fi
    TORRENT_FILE="${BATCH[$INDEX]}"
    TORRENT_BASE64=$(base64 -w0 "$TORRENT_FILE")

    JSON_PAYLOAD=$(cat <<EOF
{
  "jsonrpc":"2.0",
  "method":"aria2.addTorrent",
  "id":"$MY_ID-$start-$i",
  "params":["$RPC_TOKEN","$TORRENT_BASE64",[],{}]
}
EOF
)

    add_torrent "$TORRENT_FILE" "$JSON_PAYLOAD"
    sleep 0.2
  done
done

echo "所有任务处理完毕"

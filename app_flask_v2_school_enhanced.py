from flask import Flask, render_template, request, jsonify, send_from_directory
import requests
import os
import threading
import time
import re
import json
from datetime import datetime
from urllib.parse import urljoin, urlparse, parse_qs
from bs4 import BeautifulSoup, Tag
from typing import Optional, Tuple, Any
from concurrent.futures import ThreadPoolExecutor, as_completed
from queue import Queue

app = Flask(__name__)

DOWNLOAD_DIR = "/mnt/usb2_2-0/Public/Downloads/DOI_download"
if not os.path.exists(DOWNLOAD_DIR):
    os.makedirs(DOWNLOAD_DIR)

download_queues = {}
lock = threading.Lock()

UNPAYWALL_API_URL = "https://api.unpaywall.org/v2/"

# 更新的Sci-Hub镜像站点（按可靠性排序）
SCIHUB_BASES = [
    "https://sci-hub.se/",
    "https://sci-hub.st/",
    "https://sci-hub.ru/",
    "https://sci-hub.box/",
    "https://sci-hub.red/",
    "https://sci-hub.al/",
    "https://sci-hub.ee/",
    "https://sci-hub.lu/",
    "https://sci-hub.ren/",
    "https://sci-hub.shop/",
    "https://sci-hub.vg/",
    # 备用镜像
    "https://sci-hub.wf/",
    "https://sci-hub.hkvisa.net/",
    "https://sci-hub.lol/",
    "https://sci-hub.xyz/",
    "https://sci-hub.mksa.top/",
    "https://sci-hub.41610.org/",
    "https://sci-hub.ga/",
    "https://sci-hub.cc/",
    "https://sci-hub.bz/",
    "https://sci-hub.ac/",
]

# 增强的出版社PDF直链规则 - 针对学校网络环境优化
ENHANCED_PUBLISHER_RULES = [
    # 物理学期刊
    # APS (American Physical Society)
    (lambda doi: doi.startswith("10.1103/"), lambda doi: f"https://journals.aps.org/{doi.split('/')[1].lower()}/pdf/{doi}"),
    (lambda doi: doi.startswith("10.1103/"), lambda doi: f"https://journals.aps.org/{doi.split('/')[1].lower()}/export/{doi}"),
    (lambda doi: doi.startswith("10.1103/"), lambda doi: f"https://journals.aps.org/{doi.split('/')[1].lower()}/abstract/{doi}"),
    
    # 化学学期刊
    # ACS (American Chemical Society)
    (lambda doi: doi.startswith("10.1021/"), lambda doi: f"https://pubs.acs.org/doi/pdf/{doi}"),
    (lambda doi: doi.startswith("10.1021/"), lambda doi: f"https://pubs.acs.org/doi/pdfdirect/{doi}"),
    (lambda doi: doi.startswith("10.1021/"), lambda doi: f"https://pubs.acs.org/doi/abs/{doi}"),
    
    # RSC (Royal Society of Chemistry)
    (lambda doi: doi.startswith("10.1039/"), lambda doi: f"https://pubs.rsc.org/en/content/articlepdf/{doi.split('/')[1][:2]}/{doi.split('/')[1]}"),
    (lambda doi: doi.startswith("10.1039/"), lambda doi: f"https://pubs.rsc.org/en/content/articlelanding/{doi}"),
    
    # 医学期刊
    # Elsevier医学期刊
    (lambda doi: doi.startswith("10.1016/") and any(j in doi.lower() for j in ['lancet', 'jama', 'bmj', 'nejm']), 
     lambda doi: f"https://www.cell.com/action/showPdf?pii={doi.split('/')[-1]}"),
    
    # Nature医学系列
    (lambda doi: doi.startswith("10.1038/") and any(j in doi.lower() for j in ['nrd', 'nrm', 'nrc', 'nrg']), 
     lambda doi: f"https://www.nature.com/articles/{doi.split('/')[-1]}.pdf"),
    
    # 综合期刊
    # Science
    (lambda doi: doi.startswith("10.1126/"), lambda doi: f"https://www.science.org/doi/pdf/{doi}"),
    (lambda doi: doi.startswith("10.1126/"), lambda doi: f"https://www.science.org/doi/epdf/{doi}"),
    (lambda doi: doi.startswith("10.1126/"), lambda doi: f"https://www.science.org/doi/pdf/{doi}?download=true"),
    (lambda doi: doi.startswith("10.1126/"), lambda doi: f"https://www.science.org/doi/epdf/{doi}?download=true"),
    
    # Nature
    (lambda doi: doi.startswith("10.1038/"), lambda doi: f"https://www.nature.com/articles/{doi.split('/')[-1]}.pdf"),
    (lambda doi: doi.startswith("10.1038/"), lambda doi: f"https://www.nature.com/articles/{doi.split('/')[-1]}.epdf"),
    
    # Cell Press
    (lambda doi: doi.startswith("10.1016/"), lambda doi: f"https://www.cell.com/action/showPdf?pii={doi.split('/')[-1]}"),
    (lambda doi: doi.startswith("10.1016/"), lambda doi: f"https://www.cell.com/cms/attachment/{doi.split('/')[-1]}/fulltext.pdf"),
    
    # Springer
    (lambda doi: doi.startswith("10.1007/"), lambda doi: f"https://link.springer.com/content/pdf/{doi}.pdf"),
    (lambda doi: doi.startswith("10.1007/"), lambda doi: f"https://link.springer.com/content/pdf/{doi}.epub"),
    
    # Wiley
    (lambda doi: doi.startswith("10.1002/") or doi.startswith("10.1111/"), lambda doi: f"https://onlinelibrary.wiley.com/doi/pdfdirect/{doi}"),
    (lambda doi: doi.startswith("10.1002/") or doi.startswith("10.1111/"), lambda doi: f"https://onlinelibrary.wiley.com/doi/epdf/{doi}"),
    (lambda doi: doi.startswith("10.1002/") or doi.startswith("10.1111/"), lambda doi: f"https://advanced.onlinelibrary.wiley.com/doi/pdfdirect/{doi}?download=true"),
    
    # IEEE
    (lambda doi: doi.startswith("10.1109/"), lambda doi: f"https://ieeexplore.ieee.org/stamp/stamp.jsp?tp=&arnumber={doi.split('/')[-1]}"),
    (lambda doi: doi.startswith("10.1109/"), lambda doi: f"https://ieeexplore.ieee.org/document/{doi.split('/')[-1]}/pdf"),
    
    # ACM
    (lambda doi: doi.startswith("10.1145/"), lambda doi: f"https://dl.acm.org/doi/pdf/{doi}"),
    (lambda doi: doi.startswith("10.1145/"), lambda doi: f"https://dl.acm.org/doi/epdf/{doi}"),
    
    # PLOS
    (lambda doi: doi.startswith("10.1371/"), lambda doi: f"https://journals.plos.org/plosone/article/file?id={doi}&type=printable"),
    
    # Taylor & Francis
    (lambda doi: doi.startswith("10.1080/"), lambda doi: f"https://www.tandfonline.com/doi/pdf/{doi}"),
    (lambda doi: doi.startswith("10.1080/"), lambda doi: f"https://www.tandfonline.com/doi/epdf/{doi}"),
    
    # Frontiers
    (lambda doi: doi.startswith("10.3389/"), lambda doi: f"https://www.frontiersin.org/articles/{doi}/pdf"),
    
    # Hindawi
    (lambda doi: doi.startswith("10.1155/"), lambda doi: f"https://downloads.hindawi.com/journals/{doi.split('/')[0]}/{doi.split('/')[1]}.pdf"),
    
    # MDPI
    (lambda doi: doi.startswith("10.3390/"), lambda doi: f"https://www.mdpi.com/{doi}/pdf"),
    
    # BioMed Central
    (lambda doi: doi.startswith("10.1186/"), lambda doi: f"https://{doi.split('/')[1]}.biomedcentral.com/articles/{doi.split('/')[-1]}/pdf"),
    
    # Oxford University Press
    (lambda doi: doi.startswith("10.1093/"), lambda doi: f"https://academic.oup.com/{doi.split('/')[1]}/article-pdf/{doi.split('/')[-1]}/pdf"),
    
    # Cambridge University Press
    (lambda doi: doi.startswith("10.1017/"), lambda doi: f"https://www.cambridge.org/core/services/aop-cambridge-core/content/view/{doi.split('/')[-1]}/pdf"),
    
    # IOP Publishing (物理期刊)
    (lambda doi: doi.startswith("10.1088/"), lambda doi: f"https://iopscience.iop.org/article/{doi}/pdf"),
    
    # AIP (American Institute of Physics)
    (lambda doi: doi.startswith("10.1063/"), lambda doi: f"https://aip.scitation.org/doi/pdf/{doi}"),
    
    # EDP Sciences
    (lambda doi: doi.startswith("10.1051/"), lambda doi: f"https://www.edpsciences.org/en/articles/{doi.split('/')[-1]}/pdf"),
    
    # De Gruyter
    (lambda doi: doi.startswith("10.1515/"), lambda doi: f"https://www.degruyter.com/document/doi/{doi}/pdf"),
    
    # SAGE
    (lambda doi: doi.startswith("10.1177/"), lambda doi: f"https://journals.sagepub.com/doi/pdf/{doi}"),
    
    # Emerald
    (lambda doi: doi.startswith("10.1108/"), lambda doi: f"https://www.emerald.com/insight/content/doi/{doi}/pdf"),
    # chemrxiv 预印本
    (lambda doi: doi.startswith("10.26434/chemrxiv-"), 
     lambda doi: f"https://chemrxiv.org/engage/api-gateway/chemrxiv/assets/orp/resource/item/{doi.split('-')[-1]}/original.pdf"),
    # Science Advances/Science
    (lambda doi: doi.startswith("10.1126/"), lambda doi: f"https://www.science.org/doi/pdf/{doi}"),
    (lambda doi: doi.startswith("10.1126/"), lambda doi: f"https://www.science.org/doi/epdf/{doi}"),
    # Nature Communications
    (lambda doi: doi.startswith("10.1038/"), lambda doi: f"https://www.nature.com/articles/{doi.split('/')[-1]}.pdf"),
    (lambda doi: doi.startswith("10.1038/"), lambda doi: f"https://www.nature.com/articles/{doi.split('/')[-1]}.epdf"),
]

# 线程池参数
PARSE_WORKERS = 5
DOWNLOAD_WORKERS = 3
parse_executor = ThreadPoolExecutor(max_workers=PARSE_WORKERS)
download_executor = ThreadPoolExecutor(max_workers=DOWNLOAD_WORKERS)
# 下载任务队列（线程安全）
download_task_queue = Queue()

def normalize_doi(doi: str) -> str:
    """标准化DOI，移除前缀，确保格式正确"""
    doi = doi.strip()
    
    # 移除常见的DOI前缀
    prefixes = [
        "https://doi.org/",
        "http://doi.org/",
        "doi:",
        "DOI:",
        "https://dx.doi.org/",
        "http://dx.doi.org/",
    ]
    
    for prefix in prefixes:
        if doi.startswith(prefix):
            doi = doi[len(prefix):]
            break
    
    # 确保DOI格式正确
    if not doi.startswith("10."):
        raise ValueError(f"无效的DOI格式: {doi}")
    
    return doi

def test_url_accessibility(url: str, headers: dict, timeout: int = 10) -> Tuple[bool, str]:
    """测试URL是否可访问且返回PDF"""
    try:
        resp = requests.head(url, headers=headers, timeout=timeout, allow_redirects=True)
        content_type = resp.headers.get('Content-Type', '').lower()
        
        # 检查状态码和内容类型
        if resp.status_code == 200:
            if 'pdf' in content_type or 'application/octet-stream' in content_type:
                return True, resp.url
            elif 'text/html' in content_type:
                # 如果是HTML页面，可能需要进一步处理
                return False, resp.url
        elif resp.status_code in [301, 302, 307, 308]:
            # 重定向，尝试跟随
            return test_url_accessibility(resp.headers.get('Location', url), headers, timeout)
    except Exception as e:
        print(f"URL测试异常: {e}")
    return False, url

def extract_pdf_from_page_enhanced(url: str, headers: dict) -> Optional[str]:
    """增强的PDF链接提取，针对学校网络环境优化"""
    try:
        r = requests.get(url, headers=headers, timeout=15, allow_redirects=True)
        soup = BeautifulSoup(r.text, 'html.parser')
        
        # 1. 优先查找meta标签
        for meta in soup.find_all('meta'):
            if isinstance(meta, Tag):
                name = meta.get('name', '')
                if isinstance(name, str):
                    name = name.lower()
                else:
                    name = ''
                content = meta.get('content', '')
                if name in ['citation_pdf_url', 'pdf_url', 'fulltext_pdf_url'] and isinstance(content, str) and content:
                    full_url = urljoin(r.url, content)
                    print(f"找到meta标签PDF: {full_url}")
                    return full_url
        
        # 2. 查找PDF下载按钮和链接（针对学校网络环境）
        pdf_patterns = [
            r'\.pdf$',
            r'/pdf/',
            r'/download/',
            r'/fulltext/',
            r'/article-pdf/',
            r'/epdf/',
            r'/pdfdirect/',
            r'/download-pdf/',
            r'/get-pdf/',
            r'/full-text-pdf/',
            r'/article-pdf/',
            r'/manuscript-pdf/',
        ]
        
        # 查找所有可能的PDF链接
        for tag in soup.find_all(['a', 'link', 'button']):
            if isinstance(tag, Tag):
                href = tag.get('href', '')
                if isinstance(href, str):
                    href_lower = href.lower()
                    if any(re.search(pattern, href_lower) for pattern in pdf_patterns):
                        full_url = urljoin(r.url, href)
                        print(f"找到PDF链接: {full_url}")
                        return full_url
                
                # 检查data属性
                data_pdf = tag.get('data-pdf', '')
                if isinstance(data_pdf, str) and 'pdf' in data_pdf.lower():
                    full_url = urljoin(r.url, data_pdf)
                    print(f"找到data-pdf链接: {full_url}")
                    return full_url
                
                # 检查文本内容
                text = tag.get_text()
                if isinstance(text, str):
                    text_lower = text.lower()
                else:
                    text_lower = ''
                if any(keyword in text_lower for keyword in ['pdf', 'download', 'full text', 'fulltext']):
                    href = tag.get('href', '')
                    if isinstance(href, str) and href:
                        full_url = urljoin(r.url, href)
                        print(f"找到文本匹配的PDF链接: {full_url}")
                        return full_url
        
        # 3. 查找iframe中的PDF
        for iframe in soup.find_all('iframe'):
            if isinstance(iframe, Tag):
                src = iframe.get('src', '')
                if isinstance(src, str) and 'pdf' in src.lower():
                    full_url = urljoin(r.url, src)
                    print(f"找到iframe PDF: {full_url}")
                    return full_url
        
        # 4. 查找object/embed标签中的PDF
        for tag in soup.find_all(['object', 'embed']):
            src = None
            if isinstance(tag, Tag):
                src = tag.get('data') or tag.get('src')
            if isinstance(src, str) and 'pdf' in src.lower():
                full_url = urljoin(r.url, src)
                print(f"找到object/embed PDF: {full_url}")
                return full_url
        
        # 5. 查找JavaScript中的PDF URL
        scripts = soup.find_all('script')
        for script in scripts:
            if isinstance(script, Tag) and script.string:
                # 查找PDF URL模式
                pdf_urls = re.findall(r'["\']([^"\']*\.pdf[^"\']*)["\']', script.string)
                for pdf_url in pdf_urls:
                    if 'pdf' in pdf_url.lower():
                        full_url = urljoin(r.url, pdf_url)
                        print(f"找到JS中的PDF: {full_url}")
                        return full_url
        
        # 6. 查找特定的下载按钮类名
        download_classes = [
            'download-pdf', 'pdf-download', 'download-button', 'pdf-button',
            'fulltext-pdf', 'article-pdf', 'manuscript-pdf', 'download-link'
        ]
        
        for class_name in download_classes:
            for tag in soup.find_all(class_=class_name):
                if isinstance(tag, Tag):
                    href = tag.get('href', '')
                    if isinstance(href, str) and href:
                        full_url = urljoin(r.url, href)
                        print(f"找到类名匹配的PDF链接: {full_url}")
                        return full_url
        
    except Exception as e:
        print(f"页面解析异常: {e}")
    
    return None

def try_sci_hub_enhanced(doi: str) -> Tuple[Optional[str], Optional[str]]:
    """增强的Sci-Hub尝试，使用更新的镜像站点"""
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8"
    }
    
    for base_url in SCIHUB_BASES:
        try:
            sci_hub_url = f"{base_url}{doi}"
            print(f"尝试Sci-Hub: {sci_hub_url}")
            
            r = requests.get(sci_hub_url, headers=headers, timeout=15, allow_redirects=True)
            if r.status_code == 200:
                # 从Sci-Hub页面提取PDF链接
                pdf_url = extract_pdf_from_page_enhanced(sci_hub_url, headers)
                if pdf_url:
                    return pdf_url, sci_hub_url
        except Exception as e:
            print(f"Sci-Hub {base_url} 异常: {e}")
            continue
    
    return None, None

def resolve_pdf_url_school_enhanced(doi_input: str) -> Tuple[Optional[str], Optional[str]]:
    """针对学校网络环境优化的PDF解析函数"""
    try:
        # 标准化DOI
        doi = normalize_doi(doi_input)
        print(f"[DOI] 标准化后的DOI: {doi}")
    except ValueError as e:
        print(f"[DOI] DOI格式错误: {e}")
        return None, None
    
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
        "Referer": f"https://doi.org/{doi}"
    }
    
    print(f"[DOI] 开始学校网络环境解析: {doi}")
    
    # 1. 尝试Unpaywall API
    try:
        unpaywall_url = f"{UNPAYWALL_API_URL}{doi}?email=<EMAIL>"
        r = requests.get(unpaywall_url, timeout=15)
        data = r.json()
        if data.get('best_oa_location'):
            pdf_url = data['best_oa_location'].get('url_for_pdf')
            if pdf_url:
                print(f"[DOI] {doi} - Unpaywall 成功: {pdf_url}")
                return pdf_url, data['best_oa_location'].get('url_for_landing_page')
        print(f"[DOI] {doi} - Unpaywall 无OA PDF")
    except Exception as e:
        print(f"[DOI] {doi} - Unpaywall 异常: {e}")
    
    # 2. 尝试增强的出版社直链规则（学校网络环境优先）
    for match, build_url in ENHANCED_PUBLISHER_RULES:
        if match(doi):
            try:
                pdf_url = build_url(doi)
                print(f"[DOI] {doi} - 检测直链: {pdf_url}")
                is_accessible, final_url = test_url_accessibility(pdf_url, headers, 15)
                if is_accessible:
                    print(f"[DOI] {doi} - 直链规则成功: {final_url}")
                    return final_url, f"https://doi.org/{doi}"
            except Exception as e:
                print(f"[DOI] {doi} - 直链规则异常: {e}")
    
    print(f"[DOI] {doi} - 直链规则全部失败")
    
    # 3. 尝试从DOI页面提取PDF（学校网络环境重点）
    try:
        doi_page_url = f"https://doi.org/{doi}"
        r = requests.get(doi_page_url, headers=headers, timeout=15, allow_redirects=True)
        final_url = r.url
        
        # 从最终重定向的页面提取PDF
        pdf_url = extract_pdf_from_page_enhanced(final_url, headers)
        if pdf_url:
            print(f"[DOI] {doi} - DOI页面提取成功: {pdf_url}")
            return pdf_url, final_url
        
        # 如果DOI页面没有直接PDF，尝试从页面中查找其他线索
        soup = BeautifulSoup(r.text, 'html.parser')
        
        # 查找可能的PDF下载链接
        for link in soup.find_all('a', href=True):
            if isinstance(link, Tag):
                href = link.get('href', '') if hasattr(link, 'get') else ''
                if isinstance(href, str):
                    href_lower = href.lower()
                else:
                    href_lower = ''
                text = link.get_text() if hasattr(link, 'get_text') else ''
                if isinstance(text, str):
                    text_lower = text.lower()
                else:
                    text_lower = ''
                if any(keyword in href_lower for keyword in ['pdf', 'download', 'fulltext']) or \
                   any(keyword in text_lower for keyword in ['pdf', 'download', 'full text', 'fulltext']):
                    if isinstance(href, str):
                        full_url = urljoin(final_url, href)
                        print(f"[DOI] {doi} - 页面链接提取: {full_url}")
                        return full_url, final_url
                
    except Exception as e:
        print(f"[DOI] {doi} - DOI页面解析异常: {e}")
    
    # 4. 尝试增强的Sci-Hub（备用方案）
    print(f"[DOI] {doi} - 尝试增强Sci-Hub")
    pdf_url, referer = try_sci_hub_enhanced(doi)
    if pdf_url:
        print(f"[DOI] {doi} - Sci-Hub成功: {pdf_url}")
        return pdf_url, referer
    
    print(f"[DOI] {doi} - 所有方式均失败")
    return None, None

def download_pdf_enhanced(pdf_url: str, referer: Optional[str], save_path: str) -> bool:
    """增强的PDF下载函数"""
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "Referer": referer or '',
        "Accept": "application/pdf,application/octet-stream,*/*",
        "Accept-Language": "en-US,en;q=0.9",
        "Accept-Encoding": "gzip, deflate, br",
        "Connection": "keep-alive",
        "Upgrade-Insecure-Requests": "1"
    }
    
    try:
        with requests.get(pdf_url, headers=headers, stream=True, timeout=60) as r:
            r.raise_for_status()
            
            # 检查内容类型
            content_type = r.headers.get('Content-Type', '').lower()
            if not any(t in content_type for t in ['pdf', 'octet-stream', 'application/']):
                print(f"警告: 内容类型不是PDF: {content_type}")
            
            # 检查文件大小
            content_length = r.headers.get('Content-Length')
            if content_length and int(content_length) < 1024:  # 小于1KB可能是错误页面
                print(f"警告: 文件可能太小: {content_length} bytes")
                return False
            
            with open(save_path, 'wb') as f:
                for chunk in r.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
            
            # 验证下载的文件
            if os.path.getsize(save_path) < 1024:
                print(f"下载的文件太小，可能不是PDF: {os.path.getsize(save_path)} bytes")
                os.remove(save_path)
                return False
                
            return True
            
    except Exception as e:
        print(f"下载PDF异常 {pdf_url}: {e}")
        if os.path.exists(save_path):
            os.remove(save_path)
        return False

# 添加元信息提取函数
def fetch_metadata_crossref(doi: str) -> dict:
    """通过Crossref API获取元信息"""
    try:
        url = f"https://api.crossref.org/works/{doi}"
        headers = {"User-Agent": "Mozilla/5.0 (compatible; DOI-Resolver/1.0)"}
        r = requests.get(url, headers=headers, timeout=10)
        if r.status_code == 200:
            data = r.json().get('message', {})
            authors = []
            for author in data.get('author', []):
                given = author.get('given', '')
                family = author.get('family', '')
                if given and family:
                    authors.append(f"{given} {family}")
                elif family:
                    authors.append(family)
            
            return {
                'DOI': doi,
                'Title': data.get('title', [''])[0] if data.get('title') else '',
                'Authors': ', '.join(authors),
                'Journal': data.get('container-title', [''])[0] if data.get('container-title') else '',
                'Year': str(data.get('issued', {}).get('date-parts', [[None]])[0][0] or ''),
            }
    except Exception as e:
        print(f"元信息提取异常: {e}")
    return {'DOI': doi, 'Title': '', 'Authors': '', 'Journal': '', 'Year': ''}

def parse_doi_task(doi_input, ip):
    doi_input = doi_input.strip()
    if not doi_input:
        return {"doi": doi_input, "status": "empty", "msg": "DOI为空"}
    
    try:
        normalized_doi = normalize_doi(doi_input)
    except ValueError as e:
        return {"doi": doi_input, "status": "fail", "msg": "DOI格式错误", "reason": str(e)}
    
    # 提取元信息
    meta = fetch_metadata_crossref(normalized_doi)
    
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
        "Referer": f"https://doi.org/{normalized_doi}"
    }
    
    # 1. Unpaywall
    try:
        unpaywall_url = f"{UNPAYWALL_API_URL}{normalized_doi}?email=<EMAIL>"
        r = requests.get(unpaywall_url, timeout=15)
        data_oa = r.json()
        if data_oa.get('best_oa_location'):
            pdf_url = data_oa['best_oa_location'].get('url_for_pdf')
            if pdf_url:
                filename = normalized_doi.replace("/", "_") + ".pdf"
                referer = data_oa['best_oa_location'].get('url_for_landing_page', '')
                download_task_queue.put({
                    "doi": doi_input,
                    "pdf_url": pdf_url,
                    "referer": referer,
                    "filename": filename,
                    "ip": ip,
                    "meta": meta
                })
                return {**meta, "Status": "待下载", "URL": pdf_url, "Downloaded_File": filename}
    except Exception:
        pass
    
    # 2. 直链规则
    for match, build_url in ENHANCED_PUBLISHER_RULES:
        if match(normalized_doi):
            try:
                pdf_url = build_url(normalized_doi)
                is_accessible, final_url = test_url_accessibility(pdf_url, headers, 15)
                if is_accessible:
                    filename = normalized_doi.replace("/", "_") + ".pdf"
                    download_task_queue.put({
                        "doi": doi_input,
                        "pdf_url": final_url,
                        "referer": f"https://doi.org/{normalized_doi}",
                        "filename": filename,
                        "ip": ip,
                        "meta": meta
                    })
                    return {**meta, "Status": "待下载", "URL": final_url, "Downloaded_File": filename}
            except Exception:
                pass
    
    # 3. DOI页面爬取
    try:
        doi_page_url = f"https://doi.org/{normalized_doi}"
        r = requests.get(doi_page_url, headers=headers, timeout=15, allow_redirects=True)
        final_url = r.url
        pdf_url = extract_pdf_from_page_enhanced(final_url, headers)
        if pdf_url:
            filename = normalized_doi.replace("/", "_") + ".pdf"
            download_task_queue.put({
                "doi": doi_input,
                "pdf_url": pdf_url,
                "referer": final_url,
                "filename": filename,
                "ip": ip,
                "meta": meta
            })
            return {**meta, "Status": "待下载", "URL": pdf_url, "Downloaded_File": filename}
    except Exception:
        pass
    
    # 4. Sci-Hub
    pdf_url, referer = try_sci_hub_enhanced(normalized_doi)
    if pdf_url:
        filename = normalized_doi.replace("/", "_") + ".pdf"
        download_task_queue.put({
            "doi": doi_input,
            "pdf_url": pdf_url,
            "referer": referer,
            "filename": filename,
            "ip": ip,
            "meta": meta
        })
        return {**meta, "Status": "待下载", "URL": pdf_url, "Downloaded_File": filename}
    
    return {**meta, "Status": "失败", "URL": "", "Downloaded_File": "", "reason": "所有方式均失败"}

# 下载任务worker

def download_worker():
    while True:
        task = download_task_queue.get()
        if not task:
            continue
        
        filename = task["filename"]
        pdf_url = task["pdf_url"]
        referer = task["referer"]
        ip = task["ip"]
        meta = task.get("meta", {})
        save_path = os.path.join(DOWNLOAD_DIR, filename)
        
        # 记录到队列
        with lock:
            download_queues.setdefault(ip, [])
            download_queues[ip].append({
                **meta,
                "Status": "下载中",
                "URL": pdf_url,
                "Downloaded_File": filename,
                "time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "progress": 0
            })
        
        # 下载
        success = download_pdf_enhanced(pdf_url, referer, save_path)
        
        # 更新状态
        with lock:
            for t in download_queues[ip]:
                if t["Downloaded_File"] == filename:
                    if success:
                        t["Status"] = "成功"
                        t["progress"] = 100
                    else:
                        t["Status"] = "失败"
                        t["progress"] = 0
                    break
        
        download_task_queue.task_done()

@app.route('/')
def index():
    return render_template("index_enhanced.html")

@app.route('/submit_doi', methods=['POST'])
def submit_doi():
    data = request.get_json()
    dois = data.get("dois", [])
    ip = request.remote_addr
    results = []
    logs = []
    
    # 并发解析DOI
    futures = [parse_executor.submit(parse_doi_task, doi, ip) for doi in dois]
    for future in as_completed(futures):
        result = future.result()
        results.append(result)
        logs.append(f"{result.get('DOI', '')} - {result.get('Status', '')}")
    
    return jsonify({"results": results, "logs": logs})

@app.route('/tasks')
def tasks():
    ip = request.remote_addr
    with lock:
        tasks = download_queues.get(ip, [])
        return jsonify(tasks)

@app.route('/download/<filename>')
def download_file(filename):
    return send_from_directory(DOWNLOAD_DIR, filename, as_attachment=True)

@app.route('/all_files')
def list_all_files():
    files = os.listdir(DOWNLOAD_DIR)
    html = "<h3>服务器上已下载文件列表</h3><ul>"
    for f in files:
        html += f'<li><a href="/download/{f}">{f}</a></li>'
    html += "</ul>"
    return html

@app.route('/admin/all_tasks')
def admin_all_tasks():
    with lock:
        return jsonify(download_queues)

@app.route('/test_doi/<path:doi>')
def test_doi(doi):
    """测试DOI解析功能，支持带前缀的DOI"""
    pdf_url, reason = resolve_pdf_url_school_enhanced(doi)
    return jsonify({
        "doi": doi,
        "pdf_url": pdf_url,
        "reason": reason,
        "success": pdf_url is not None
    })

@app.route('/health')
def health_check():
    """健康检查接口"""
    return jsonify({
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "download_dir": DOWNLOAD_DIR,
        "queue_count": len(download_queues)
    })

if __name__ == "__main__":
    # 启动下载worker线程池
    for _ in range(DOWNLOAD_WORKERS):
        t = threading.Thread(target=download_worker, daemon=True)
        t.start()
    app.run(host="0.0.0.0", port=7788) 

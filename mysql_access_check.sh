[root@localhost ~]# cat /opt/mysql/mysql_access_check.sh 
#!/bin/bash

GENERAL_LOG="/var/log/mysql/general.log"
TIME_WINDOW=300               # 5分钟
FAIL_THRESHOLD=3
ACCESS_THRESHOLD=15
DINGTALK_WEBHOOK="https://oapi.dingtalk.com/robot/send?access_token=82789bdc771ee14ab3e363972504b6a5a8db0b1a6af704aab5de909b572a859b"

START_TIME=$(date --utc --date="-$TIME_WINDOW seconds" +%Y-%m-%dT%H:%M:%S)
CURRENT_TIME=$(date --utc "+%Y-%m-%d %H:%M:%S UTC")

RECENT_LOG=$(awk -v start="$START_TIME" '$1 >= start' "$GENERAL_LOG")

FAIL_COUNTS=$(echo "$RECENT_LOG" | grep "Access denied for user" | awk -F"'" '{print $2}' | sort | uniq -c | sort -nr)
SUCCESS_COUNTS=$(echo "$RECENT_LOG" | grep -P "Connect\s+\S+@\S+\s+on" | grep -v "Access denied" | awk '{print $4}' | sort | uniq -c | sort -nr)

send_dingtalk() {
  local content="$1"
  curl -s -H "Content-Type: application/json" \
    -X POST \
    -d "{\"msgtype\": \"markdown\",\"markdown\": {\"title\": \"MySQL访问告警\",\"text\": \"$content\"}}" \
    "$DINGTALK_WEBHOOK"
}

fail_table=""
while read -r line; do
  count=$(echo "$line" | awk '{print $1}')
  userhost=$(echo "$line" | awk '{print $2}')
  if [[ "$count" =~ ^[0-9]+$ ]] && [ "$count" -ge "$FAIL_THRESHOLD" ]; then
    fail_table="$fail_table| 登录失败 | $userhost | $count | $((TIME_WINDOW/60)) |\n"
  fi
done <<< "$FAIL_COUNTS"

access_table=""
while read -r line; do
  count=$(echo "$line" | awk '{print $1}')
  userhost=$(echo "$line" | awk '{print $2}')
  if [[ "$count" =~ ^[0-9]+$ ]] && [ "$count" -ge "$ACCESS_THRESHOLD" ]; then
    access_table="$access_table| 访问频繁 | $userhost | $count | $((TIME_WINDOW/60)) |\n"
  fi
done <<< "$SUCCESS_COUNTS"

if [ -n "$fail_table" ]; then
  content="## 🚨 MySQL 登录失败告警\n\n> 时间：$CURRENT_TIME\n> 统计时间窗口：最近 $((TIME_WINDOW/60)) 分钟\n\n| 类型 | 用户主机 | 次数 | 时间窗口(分钟) |\n| ---- | -------- | ---- | -------------- |\n$fail_table\n> 请尽快排查，防范暴力破解等风险。"
  send_dingtalk "$content"
fi

if [ -n "$access_table" ]; then
  content="## 🚨 MySQL 访问频繁告警\n\n> 时间：$CURRENT_TIME\n> 统计时间窗口：最近 $((TIME_WINDOW/60)) 分钟\n\n| 类型 | 用户主机 | 次数 | 时间窗口(分钟) |\n| ---- | -------- | ---- | -------------- |\n$access_table\n> 请确认是否为正常业务流量。"
  send_dingtalk "$content"
fi

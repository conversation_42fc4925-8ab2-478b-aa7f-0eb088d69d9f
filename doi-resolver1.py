#!/usr/bin/env python3
import requests, sys, time, os
from urllib.parse import quote_plus

def get_doi(title):
    api = f"https://api.crossref.org/works?query.title={quote_plus(title)}"
    try:
        r = requests.get(api, timeout=10)
        r.raise_for_status()
        items = r.json()['message']['items']
        if not items:
            return None
        return items[0]['DOI']
    except Exception:
        return None

def get_pdf_url(doi):
    return f"https://doi.org/{doi}"

def run(infile, out_dir):
    base_name = os.path.basename(infile)
    name_only = os.path.splitext(base_name)[0]
    links_path = os.path.join(out_dir, f"{name_only}.links")

    success = []
    fail = []

    with open(infile, 'r') as fin:
        for line in fin:
            title = line.strip()
            if not title:
                continue
            print(f"[+] 查找文献：{title}")
            doi = get_doi(title)
            if doi:
                url = get_pdf_url(doi)
                success.append(url)
                print(f"   成功：{url}")
            else:
                fail.append(title)
                print(f"   失败：{title}")
            time.sleep(1)

    # 写出成功链接
    if success:
        with open(links_path, 'w') as f:
            for url in success:
                f.write(url + "\n")

    # 清空原文件并写入未成功的文献名
    with open(infile, 'w') as f:
        for title in fail:
            f.write(title + "\n")

if __name__ == '__main__':
    if len(sys.argv) < 3:
        print("用法: python3 doi_resolver.py 文献文件.txt 输出目录")
        sys.exit(1)
    run(sys.argv[1], sys.argv[2])
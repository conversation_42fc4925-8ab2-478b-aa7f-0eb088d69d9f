<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <title>文献下载平台</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/normalize/8.0.1/normalize.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        * {
            box-sizing: border-box;
        }
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            line-height: 1.5;
            margin: 0;
            padding: 0;
            background-color: #f8f9fa;
        }
    </style>
</head>
<body>

<nav class="navbar navbar-dark bg-primary mb-4">
  <div class="container-fluid">
    <a class="navbar-brand" href="#">文献下载平台</a>
  </div>
</nav>

<div class="container">
  <div class="row">
    <div class="col-md-6 mb-4">
      <div class="card">
        <div class="card-header bg-primary text-white">提交 DOI 下载</div>
        <div class="card-body">
          <textarea id="dois" class="form-control" rows="5" placeholder="每行一个 DOI"></textarea>
          <button class="btn btn-primary mt-3 w-100" id="submit_doi_btn" onclick="submitDOI()">提交下载</button>
          <a href="/all_files" target="_blank" class="btn btn-secondary mt-3 w-100">访问所有已下载文件</a>
        </div>
      </div>

      <div class="card mt-4">
        <div class="card-header bg-secondary text-white">下载进度</div>
        <div class="card-body" id="progress_list">无任务</div>
      </div>

      <div class="card mt-4">
        <div class="card-header bg-dark text-white">输出台</div>
        <div class="card-body" id="output_console" style="font-family:monospace;white-space:pre;max-height:300px;overflow:auto;">无输出</div>
      </div>
    </div>

    <div class="col-md-6 mb-4">
      <div class="card">
        <div class="card-header bg-success text-white">完成文件下载</div>
        <div class="card-body" id="done_list">无文件</div>
      </div>
    </div>
  </div>
</div>

<script>
function submitDOI() {
    const btn = document.getElementById("submit_doi_btn");
    btn.disabled = true;
    btn.innerText = "请稍候...";
    document.getElementById("output_console").innerText = "正在处理，请稍候...";

    const dois = document.getElementById("dois").value.trim().split("\n").filter(x => x.trim());
    if (!dois.length) {
        alert("请输入至少一个 DOI");
        btn.disabled = false;
        btn.innerText = "提交下载";
        document.getElementById("output_console").innerText = "无输出";
        return;
    }
    fetch("/submit_doi", {
        method: "POST",
        headers: {"Content-Type": "application/json"},
        body: JSON.stringify({dois})
    }).then(res => res.json())
      .then(data => {
        let msg = `成功提交 ${data.success.length} 个任务。`;
        if (data.fail.length) {
            msg += `\n失败 ${data.fail.length} 个：\n` + data.fail.map(f => `${f.doi} - ${f.msg}`).join("\n");
        }
        alert(msg);
        loadTasks();
        // 输出台展示日志
        document.getElementById("output_console").innerText = (data.logs && data.logs.length) ? data.logs.join("\n") : "无输出";
    })
    .finally(() => {
        btn.disabled = false;
        btn.innerText = "提交下载";
    });
}

function loadTasks() {
    fetch("/tasks").then(res => res.json()).then(tasks => {
        let progressHtml = "", doneHtml = "";
        tasks.forEach(t => {
            const name = t.filename;
            if (t.status === "downloading") {
                progressHtml += `
                <div class="border rounded p-2 mb-2">
                    <strong>${name}</strong><br/>
                    <a href="${t.pdf_url}" target="_blank">${t.doi}</a><br/>
                    状态: ${t.status}<br/>
                    进度: ${t.progress || 0}%<br/>
                    <div class="progress mt-2">
                      <div class="progress-bar" role="progressbar" style="width: ${t.progress || 0}%" aria-valuenow="${t.progress || 0}" aria-valuemin="0" aria-valuemax="100">${t.progress || 0}%</div>
                    </div>
                    提交时间: ${t.time}
                </div>`;
            }
            if (t.status === "complete") {
                doneHtml += `
                <div class="border rounded p-2 mb-2">
                    <strong>${name}</strong><br/>
                    <a href="/download/${encodeURIComponent(t.filename)}" target="_blank" class="btn btn-sm btn-success mt-2">下载文件</a><br/>
                    提交时间: ${t.time}
                </div>`;
            }
        });
        document.getElementById("progress_list").innerHTML = progressHtml || "<p class='text-muted'>无任务</p>";
        document.getElementById("done_list").innerHTML = doneHtml || "<p class='text-muted'>无文件</p>";
    });
}

window.onload = function() {
    loadTasks();
    setInterval(loadTasks, 5000);
};
</script>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 
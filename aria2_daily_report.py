#!/usr/bin/env python3
import json
import urllib.request
import datetime
import os
import subprocess

LOG_PATH = "/mnt/usb2_2-0/log/aria2_daily_report.log"
RPC_URL = "http://localhost:6800/jsonrpc"

def get_aria2_status():
    data = {
        "jsonrpc": "2.0",
        "id": "qwer",
        "method": "aria2.getGlobalStat"
    }
    req = urllib.request.Request(
        RPC_URL,
        data=json.dumps(data).encode("utf-8"),
        headers={"Content-Type": "application/json"}
    )
    with urllib.request.urlopen(req) as resp:
        result = json.loads(resp.read())
    return result["result"]

def get_history_finished_count():
    cmd = ["find", "/mnt/usb2_2-0/aria2_downloads", "-type", "f"]
    result = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
    return len(result.stdout.strip().split("\n")) if result.stdout.strip() else 0

def write_log():
    try:
        status = get_aria2_status()
        num_active = int(status["numActive"])
        num_waiting = int(status["numWaiting"])
        num_stopped = int(status["numStoppedTotal"])
        download_speed = int(status["downloadSpeed"])

        history_finished = get_history_finished_count()
        total_finished = num_stopped + history_finished
        total_tasks = num_active + num_waiting + num_stopped + history_finished
        remaining_tasks = num_active + num_waiting

        progress = (total_finished / total_tasks * 100) if total_tasks > 0 else 0
        now = datetime.datetime.now().strftime("[%Y-%m-%d %H:%M]")
        log_content = (
            f"{now} 下载日报\n"
            f"总任务数（含历史）: {total_tasks}\n"
            f"已完成任务数（含历史）: {total_finished}\n"
            f"当前下载中任务数: {num_active}\n"
            f"等待下载任务数: {num_waiting}\n"
            f"剩余任务数: {remaining_tasks}\n"
            f"当前下载速度: {download_speed / (1024 * 1024):.2f} MB/s\n"
            f"总体完成进度: {progress:.2f}%\n\n"
        )

        os.makedirs(os.path.dirname(LOG_PATH), exist_ok=True)
        with open(LOG_PATH, "w") as f:  # 覆盖写
            f.write(log_content)
        print("日志已更新:", LOG_PATH)
    except Exception as e:
        print("日志生成失败:", str(e))

if __name__ == "__main__":
    write_log()

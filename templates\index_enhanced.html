<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DOI PDF 下载器 -</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .table-container { max-height: 400px; overflow-y: auto; }
        .status-success { color: #28a745; font-weight: bold; }
        .status-downloading { color: #007bff; }
        .status-failed { color: #dc3545; }
        .status-pending { color: #ffc107; }
        #output_console { font-family: monospace; white-space: pre; max-height: 200px; overflow: auto; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1 class="text-center mb-4">DOI PDF 下载器 </h1>
        
        <div class="row">
            <div class="col-md-6 mb-4">
                <div class="card">
                    <div class="card-header bg-primary text-white">提交 DOI 下载</div>
                    <div class="card-body">
                        <textarea id="dois" class="form-control" rows="5" placeholder="每行一个 DOI"></textarea>
                        <button class="btn btn-primary mt-3 w-100" id="submit_doi_btn" onclick="submitDOI()">提交下载</button>
                        <a href="/all_files" target="_blank" class="btn btn-secondary mt-3 w-100">访问所有已下载文件</a>
                    </div>
                </div>

                <div class="card mt-4">
                    <div class="card-header bg-secondary text-white">下载进度</div>
                    <div class="card-body" id="progress_list">无任务</div>
                </div>

                <div class="card mt-4">
                    <div class="card-header bg-dark text-white">输出台</div>
                    <div class="card-body" id="output_console">无输出</div>
                </div>
            </div>

            <div class="col-md-6 mb-4">
                <div class="card">
                    <div class="card-header bg-success text-white">完成文件下载</div>
                    <div class="card-body" id="done_list">无文件</div>
                </div>
                <div class="card mt-4">
                    <div class="card-header bg-info text-white">DOI元信息与状态</div>
                    <div class="card-body">
                        <div class="table-container">
                            <table class="table table-bordered table-sm" id="meta_table">
                                <thead>
                                    <tr>
                                        <th>DOI</th><th>Title</th><th>Authors</th><th>Journal</th><th>Year</th><th>Status</th><th>URL</th><th>Downloaded_File</th>
                                    </tr>
                                </thead>
                                <tbody></tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script>
        function submitDOI() {
            const btn = document.getElementById("submit_doi_btn");
            btn.disabled = true;
            btn.innerText = "请稍候...";
            document.getElementById("output_console").innerText = "正在处理，请稍候...";
            
            const dois = document.getElementById("dois").value.trim().split("\n").filter(x => x.trim());
            if (!dois.length) {
                alert("请输入至少一个 DOI");
                btn.disabled = false;
                btn.innerText = "提交下载";
                document.getElementById("output_console").innerText = "无输出";
                return;
            }
            
            fetch("/submit_doi", {
                method: "POST",
                headers: {"Content-Type": "application/json"},
                body: JSON.stringify({dois})
            })
            .then(res => res.json())
            .then(data => {
                // 更新元信息表格
                const tbody = document.querySelector('#meta_table tbody');
                tbody.innerHTML = '';
                
                data.results.forEach(row => {
                    const tr = document.createElement('tr');
                    tr.innerHTML = `<td>${row.DOI||row.doi||''}</td><td>${row.Title||''}</td><td>${row.Authors||''}</td><td>${row.Journal||''}</td><td>${row.Year||''}</td><td class='${row.Status=="成功"?"text-success":(row.Status=="失败"?"text-danger":"") }'>${row.Status||row.status||''}${row.reason?('<br>'+row.reason):''}</td><td><a href='${row.URL||"#"}' target='_blank'>${row.URL||''}</a></td><td>${row.Downloaded_File?('<a href="/download/'+row.Downloaded_File+'">'+row.Downloaded_File+'</a>'):''}</td>`;
                    tbody.appendChild(tr);
                });
                loadTasks();
                document.getElementById("output_console").innerText = (data.logs && data.logs.length) ? data.logs.join("\n") : "无输出";
              })
            .finally(() => {
                btn.disabled = false;
                btn.innerText = "提交下载";
            });
        }
        
        function loadTasks() {
            fetch("/tasks").then(res => res.json()).then(tasks => {
                let progressHtml = "", doneHtml = "";
                tasks.forEach(t => {
                    const name = t.filename || t.Downloaded_File;
                    if (t.status === "downloading" || t.Status === "下载中") {
                        progressHtml += `
                        <div class="border rounded p-2 mb-2">
                            <strong>${name}</strong><br/>
                            <a href="${t.pdf_url||t.URL}" target="_blank">${t.doi||t.DOI}</a><br/>
                            状态: ${t.status||t.Status}<br/>
                            进度: ${t.progress || 0}%<br/>
                            <div class="progress mt-2">
                              <div class="progress-bar" role="progressbar" style="width: ${t.progress || 0}%" aria-valuenow="${t.progress || 0}" aria-valuemin="0" aria-valuemax="100">${t.progress || 0}%</div>
                            </div>
                            提交时间: ${t.time||''}
                        </div>`;
                    }
                    if (t.status === "complete" || t.Status === "成功") {
                        doneHtml += `
                        <div class="border rounded p-2 mb-2">
                            <strong>${name}</strong><br/>
                            <a href="/download/${encodeURIComponent(t.filename||t.Downloaded_File)}" target="_blank" class="btn btn-sm btn-success mt-2">下载文件</a><br/>
                            提交时间: ${t.time||''}
                        </div>`;
                    }
                });
                document.getElementById("progress_list").innerHTML = progressHtml || "<p class='text-muted'>无任务</p>";
                document.getElementById("done_list").innerHTML = doneHtml || "<p class='text-muted'>无文件</p>";
                
                // 更新元信息表格
                const tbody = document.querySelector('#meta_table tbody');
                if (tbody.children.length === 0) {
                    tasks.forEach(t => {
                        const tr = document.createElement('tr');
                        tr.innerHTML = `<td>${t.DOI||t.doi||''}</td><td>${t.Title||''}</td><td>${t.Authors||''}</td><td>${t.Journal||''}</td><td>${t.Year||''}</td><td class='${t.Status=="成功"?"text-success":(t.Status=="失败"?"text-danger":"") }'>${t.Status||t.status||''}${t.reason?('<br>'+t.reason):''}</td><td><a href='${t.URL||t.pdf_url||"#"}' target='_blank'>${t.URL||t.pdf_url||''}</a></td><td>${t.Downloaded_File?('<a href="/download/'+t.Downloaded_File+'">'+t.Downloaded_File+'</a>'):''}</td>`;
                        tbody.appendChild(tr);
                    });
                }
            });
        }
        
        window.onload = function() {
            loadTasks();
            setInterval(loadTasks, 5000);
        };
    </script>
</body>
</html>

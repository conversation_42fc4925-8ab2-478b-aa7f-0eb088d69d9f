#!/bin/bash

DING_WEBHOOK="https://oapi.dingtalk.com/robot/send?access_token=5f85e0d07498fabceb965d4e5fb811628a20d3120d75cd00fff91b909e3db31c"
LOG_FILE="/var/log/system_monitor.log"
MAX_LOG_SIZE="10M"  # 日志轮转大小

# 报警阈值配置
CPU_THRESHOLD=90       # CPU使用率% (超过此值报警)
IO_AWAIT_THRESHOLD=50  # IO等待时间ms (超过此值报警)
IO_UTIL_THRESHOLD=80   # 磁盘利用率% (超过此值报警)
LOAD_THRESHOLD=$(nproc) # 系统负载阈值 (建议设置为CPU核心数)
DISK_USAGE_THRESHOLD=80  # 磁盘使用率% (超过此值报警)

# 连续检测次数(避免瞬时波动误报)
ALERT_COUNT=2

# 初始化 ==========================================
mkdir -p $(dirname $LOG_FILE)
touch $LOG_FILE

# 日志轮转检查
if [ $(stat -c %s $LOG_FILE) -gt $(numfmt --from=iec $MAX_LOG_SIZE) ]; then
    mv $LOG_FILE $LOG_FILE.old
fi

# 钉钉报警函数
ding_alert() {
    local message="$1"
    local timestamp=$(date "+%Y-%m-%d %H:%M:%S")
    
    curl -s "$DING_WEBHOOK" \
        -H "Content-Type: application/json" \
        -d "{
            \"msgtype\": \"markdown\",
            \"markdown\": {
                \"title\": \"系统监控报警\",
                \"text\": \"**[$timestamp] 系统异常** \n\n $message \n\n---
                \n> **服务器**: $(hostname) \n> **IP**: $(hostname -I | awk '{print $1}') \n> **时间**: $timestamp \"
            },
            \"at\": {
                \"isAtAll\": false
            }
        }" >/dev/null 2>&1
    
    echo "$timestamp [ALERT] $message" >> $LOG_FILE
}

# 监控主循环 ======================================
cpu_counter=0
io_counter=0
declare -A disk_counters  # 为每个磁盘创建独立的计数器

while true; do
    # 获取CPU使用率
    cpu_usage=$(top -bn1 | grep "Cpu(s)" | sed "s/.*, *\([0-9.]*\)%* id.*/\1/" | awk '{print 100 - $1}')
    
    # 获取系统负载
    load_avg=$(awk '{print $1}' /proc/loadavg)
    
    # 获取磁盘I/O指标 (取sda设备示例)
    io_stats=$(iostat -d -x sda 1 2 | awk '/sda/{i++; if(i==2) print $0}')
    io_await=$(echo $io_stats | awk '{print $10}')
    io_util=$(echo $io_stats | awk '{print $14}')
    
    # 获取进程状态 (D状态=不可中断睡眠)
    d_state=$(ps -eo state | grep -c 'D')
    
    # 日志记录
    timestamp=$(date "+%Y-%m-%d %H:%M:%S")
    echo "$timestamp CPU:${cpu_usage}% Load:${load_avg} IO_await:${io_await}ms IO_util:${io_util}% D-process:${d_state}" >> $LOG_FILE
    
    # 检测CPU过载
    if (( $(echo "$cpu_usage > $CPU_THRESHOLD" | bc -l) )) || 
       (( $(echo "$load_avg > $LOAD_THRESHOLD" | bc -l) )); then
        ((cpu_counter++))
    else
        cpu_counter=0
    fi
    
    # 检测磁盘I/O瓶颈
    if (( $(echo "$io_await > $IO_AWAIT_THRESHOLD" | bc -l) )) || 
       (( $(echo "$io_util > $IO_UTIL_THRESHOLD" | bc -l) )) || 
       [ "$d_state" -gt 5 ]; then
        ((io_counter++))
    else
        io_counter=0
    fi
    
    # 检测磁盘空间使用情况 (修复部分)
    df -h --output=source,pcent,target | grep -vE '^Filesystem|tmpfs|devtmpfs' | while read -r disk usage mount; do
        # 去除百分号
        usage=${usage//%/}
        
        # 初始化磁盘计数器
        if [ -z "${disk_counters[$disk]}" ]; then
            disk_counters[$disk]=0
        fi
        
        # 检查磁盘空间
        if (( $(echo "$usage > $DISK_USAGE_THRESHOLD" | bc -l) )); then
            ((disk_counters[$disk]++))
            
            # 连续超过阈值才报警
            if [ "${disk_counters[$disk]}" -ge "$ALERT_COUNT" ]; then
                message="**磁盘空间警报** \n> 磁盘: $disk \n> 挂载点: $mount \n> 使用率: ${usage}% (阈值: ${DISK_USAGE_THRESHOLD}%)"
                ding_alert "$message"
                disk_counters[$disk]=0  # 重置计数器
            fi
        else
            disk_counters[$disk]=0  # 重置计数器
        fi
    done
    
    # 触发报警
    if [ "$cpu_counter" -ge "$ALERT_COUNT" ]; then
        message="**CPU过载警报** \n> 当前使用率: ${cpu_usage}% (阈值: ${CPU_THRESHOLD}%) \n> 系统负载: ${load_avg} (阈值: ${LOAD_THRESHOLD})"
        ding_alert "$message"
        cpu_counter=0
    fi
    
    if [ "$io_counter" -ge "$ALERT_COUNT" ]; then
        message="**磁盘I/O警报** \n> 等待时间: ${io_await}ms (阈值: ${IO_AWAIT_THRESHOLD}ms) \n> 磁盘利用率: ${io_util}% (阈值: ${IO_UTIL_THRESHOLD}%) \n> D状态进程数: ${d_state}"
        ding_alert "$message"
        io_counter=0
    fi
    
    sleep 5
done